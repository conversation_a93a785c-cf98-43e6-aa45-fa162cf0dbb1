---
title: 那些年用过的CSS奇妙用法之能用CSS就不用JS技巧系列
date: 2023-08-27
updated: 2023-09-09
tags:
  - 前端
  - 开发
  - css3
categories:
  - 前端
  - 开发
cover: https://img02.anheyu.com/adminuploads/1/2023/08/27/64eab7e7b0b2b.webp
description: 嵌套的圆角在遇到内外两层圆角时，可以通过 CSS 变量动态去计算内部的圆角，看起来会更加和谐
ai: true
---

## 嵌套的圆角

在遇到内外两层圆角时，可以通过 CSS 变量动态去计算内部的圆角，看起来会更加和谐

### 核心代码

```css
.parent {
  --anzhiyu-nested-radius: calc(var(--radius) - var(--padding));
}
.nested {
  border-radius: var(--anzhiyu-nested-radius);
}
```

《css嵌套的圆角》在线运行[1024code](https://1024code.com/)

## 视图转换动画 View Transitions API

我们先从一个简单的例子来认识一下。

```html
<div class="list" id="list">
  <div class="item">1</div>
  <div class="item">2</div>
  <div class="item">3</div>
  <div class="item">4</div>
  <div class="item">5</div>
</div>
```

这个API可以让我们在页面切换时实现平滑的过渡动画，提升用户体验。
