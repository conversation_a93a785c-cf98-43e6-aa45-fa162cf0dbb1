---
ai: true
categories:
- 工具
cover: https://images.unsplash.com/photo-1618401471353-b98afee0b2eb?ixlib=rb-4.0.3&auto=format&fit=crop&w=2088&q=80
date: 2025-04-18
description: 推荐一些超级好用的 VSCode 插件，让你的开发效率翻倍
tags:
- VSCode
- 插件
- 开发工具
title: vscode 插件 How 外挂推荐
top_group_index: 8
top_img: https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&auto=format&fit=crop&w=2069&q=80
updated: 2025-04-18
---

# VSCode 插件推荐

作为前端开发者，VSCode 是我们最常用的编辑器。今天推荐一些超级好用的插件。

## 必装插件

### 1. Auto Rename Tag
自动重命名配对的 HTML/XML 标签

### 2. Bracket Pair Colorizer
为括号添加颜色，让代码结构更清晰

### 3. Chinese (Simplified)
中文语言包

### 4. ES7+ React/Redux/React-Native snippets
React 开发必备代码片段

### 5. GitLens
增强 Git 功能，显示代码作者和提交历史

## 主题插件

### One Dark Pro
经典的暗色主题

### Material Icon Theme
美观的文件图标主题

## 实用工具

### Live Server
本地开发服务器

### Prettier
代码格式化工具

### Thunder Client
API 测试工具，类似 Postman

这些插件能大大提升开发效率，强烈推荐！
