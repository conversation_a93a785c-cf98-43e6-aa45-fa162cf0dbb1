---
title: 弹性盒子布局完全指南
date: 2023-05-20
updated: 2023-08-01
tags:
  - CSS
  - 前端
  - 布局
categories:
  - 技巧
cover: https://img02.anheyu.com/adminuploads/1/2023/05/20/646e5c8b7bb6.webp
top_img: https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80
description: CSS弹性盒子布局详解，从入门到精通，掌握现代CSS布局的核心技术
swiper_index: 5
top_group_index: 5
ai: true
---

## 什么是Flexbox？

Flexbox（弹性盒子布局）是CSS3中的一种布局方式，它能够更加高效地安排、分布和对齐容器中的项目，即使它们的大小未知或是动态的。

## 基本概念

### 主轴和交叉轴

- **主轴（main axis）**：Flexbox的主要轴线
- **交叉轴（cross axis）**：垂直于主轴的轴线

### 容器和项目

- **容器（container）**：设置了 `display: flex` 的父元素
- **项目（item）**：容器内的直接子元素

## 容器属性

### display

```css
.container {
  display: flex; /* 或者 inline-flex */
}
```

### flex-direction

控制主轴的方向：

```css
.container {
  flex-direction: row | row-reverse | column | column-reverse;
}
```

### flex-wrap

控制是否换行：

```css
.container {
  flex-wrap: nowrap | wrap | wrap-reverse;
}
```

### justify-content

控制主轴上的对齐方式：

```css
.container {
  justify-content: flex-start | flex-end | center | space-between | space-around | space-evenly;
}
```

### align-items

控制交叉轴上的对齐方式：

```css
.container {
  align-items: stretch | flex-start | flex-end | center | baseline;
}
```

## 项目属性

### flex-grow

定义项目的放大比例：

```css
.item {
  flex-grow: 0; /* 默认值，不放大 */
}
```

### flex-shrink

定义项目的缩小比例：

```css
.item {
  flex-shrink: 1; /* 默认值，等比缩小 */
}
```

### flex-basis

定义项目的初始大小：

```css
.item {
  flex-basis: auto; /* 默认值 */
}
```

### align-self

允许单个项目有与其他项目不一样的对齐方式：

```css
.item {
  align-self: auto | flex-start | flex-end | center | baseline | stretch;
}
```

## 实际应用案例

### 水平垂直居中

```css
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
```

### 等高布局

```css
.container {
  display: flex;
}

.item {
  flex: 1; /* 等分剩余空间 */
}
```

### 响应式导航

```css
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 768px) {
  .nav {
    flex-direction: column;
  }
}
```

## 总结

Flexbox是现代CSS布局的重要工具，掌握它能够让我们更轻松地实现各种复杂的布局效果。通过合理使用容器属性和项目属性，我们可以创建出既美观又实用的网页布局。
