@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    博客文章封面自动配置脚本
echo ========================================
echo.
echo 选择运行模式：
echo 1. 只为没有封面的文章添加封面（推荐）
echo 2. 强制更新所有文章的封面
echo.
set /p choice=请输入选择 (1 或 2):

if "%choice%"=="1" (
    echo.
    echo 🚀 正在为没有封面的文章配置高质量封面图片...
    echo.
    python scripts/update_covers.py
) else if "%choice%"=="2" (
    echo.
    echo 🔄 强制更新模式：正在为所有文章重新配置封面图片...
    echo.
    python scripts/update_covers.py --force
) else (
    echo.
    echo ❌ 无效选择，默认使用模式1
    echo.
    python scripts/update_covers.py
)

echo.
echo ========================================
echo 脚本执行完成！
echo ========================================
echo.
pause
