---
title: 什么是CDN？它解决了什么难题？国内CDN加Vercel，无服务器使用办法
date: 2023-08-26
updated: 2023-09-02
tags:
  - CDN
  - 网络安全
  - Vercel
categories:
  - 网络安全
cover: https://img02.anheyu.com/adminuploads/1/2023/08/26/64e9b8c5d4b2a.webp
top_img: https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80
description: 最近遇到了部分地区vercel即便绑定了自定义域名任然有部分用户无法访问被阻断的情况，于是就打算给vercel套一层国内CDN
ai: true
---

最近遇到了部分地区vercel即便绑定了自定义域名任然有部分用户无法访问被阻断的情况，于是就打算给vercel套一层国内CDN，既然用户无法访问vercel的ip，那么就让用户去访问国内 CDN的节点，然后再让节点去回源访问vercel即可。

该教程适用于无服务器的情况，如果有自己的服务器就没必要多此一举，而是可以直接使用国内CDN即可。注意大部分国内 CDN使用需要域名进行备案。

## 什么是CDN?

CDN英文全称Content Delivery Network，中文翻译即为内容分发网络。它是建立并覆盖在承载网之上，由分布在不同区域的边缘节点服务器群组成的分布式网络。

简而言之就是，厂商在全国各地都有很多很多的服务器，每一个服务器我们称之为节点，很多的节点组成了CDN。

## CDN工作原理

CDN的工作原理就是将源站的资源缓存到位于全国各地的CDN节点上，用户请求资源时，就近返回节点上缓存的资源，而不需要每个用户的请求都回您的源站获取，避免网络拥塞、分担源站压力，保证用户访问资源的速度和体验。

在用户与源站之间加了一层CDN，用户先访问 CDN的节点，CDN节点再去源站拿取内容，当用户第二次访问时，CDN节点就可以直接返回缓存的内容，而不需要再次访问源站。

## 为什么需要CDN？

1. **加速访问**：通过就近访问，减少网络延迟
2. **减轻服务器压力**：分散请求到各个节点
3. **提高可用性**：即使源站出现问题，CDN仍可提供服务
4. **节省带宽成本**：减少源站的带宽消耗

## Vercel + 国内CDN配置

### 步骤1：准备工作

1. 确保你的Vercel项目已经部署成功
2. 准备一个已备案的域名
3. 选择一个国内CDN服务商（如阿里云、腾讯云、又拍云等）

### 步骤2：配置CDN

1. 在CDN控制台添加加速域名
2. 源站类型选择"源站域名"
3. 源站地址填写你的Vercel域名
4. 配置HTTPS证书
5. 设置缓存规则

### 步骤3：DNS解析

将你的域名CNAME解析到CDN提供的地址即可。

这样配置后，国内用户访问你的域名时，会先到达CDN节点，然后CDN节点再去访问Vercel，实现了加速和稳定性的双重保障。
